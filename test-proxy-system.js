#!/usr/bin/env node

const ProxyManager = require('./proxy-manager');

async function testProxySystem() {
    console.log('🧪 Teste das neue Proxy-System...\n');

    try {
        // Erstelle ProxyManager-Instanz
        console.log('1. Erstelle ProxyManager...');
        const proxyManager = new ProxyManager();

        // Warte auf initiale Proxy-Liste und prüfe regelmäßig
        console.log('2. Warte auf initiale Proxy-Liste...');
        let attempts = 0;
        const maxAttempts = 30; // 30 Sekunden

        while (attempts < maxAttempts) {
            await sleep(1000);
            attempts++;

            if (proxyManager.getProxyCount() > 0) {
                console.log(`✓ Proxys geladen nach ${attempts} Sekunden`);
                break;
            }

            if (attempts % 5 === 0) {
                console.log(`   Warte noch... (${attempts}/${maxAttempts} Sekunden)`);
            }
        }

        if (proxyManager.getProxyCount() === 0) {
            console.log('⚠️ Timeout beim <PERSON> der Pro<PERSON>, fahre trotzdem fort...');
        }

        // Zeige Statistiken
        console.log('3. Proxy-Statistiken:');
        const stats = proxyManager.getStats();
        console.log(`   - Anzahl Proxys: ${stats.totalProxies}`);
        console.log(`   - Länder: ${stats.countries.join(', ')}`);
        console.log(`   - Protokolle: ${stats.protocols.join(', ')}`);
        console.log(`   - Letzte Aktualisierung: ${stats.lastUpdate}`);

        // Teste zufälligen Proxy
        console.log('\n4. Teste zufälligen Proxy...');
        const randomProxy = proxyManager.getRandomProxy();
        if (randomProxy) {
            console.log(`   - Gewählter Proxy: ${randomProxy}`);

            // Teste Proxy
            const [ip, port] = randomProxy.split(':');
            const isWorking = await proxyManager.testProxy({ ip, port: parseInt(port) });
            console.log(`   - Proxy funktioniert: ${isWorking ? '✅' : '❌'}`);
        } else {
            console.log('   - ❌ Keine Proxys verfügbar');
        }

        // Zeige Proxy-Liste
        console.log('\n5. Verfügbare Proxys:');
        const proxyList = proxyManager.getProxyList();
        if (proxyList.length > 0) {
            proxyList.slice(0, 5).forEach((proxy, index) => {
                console.log(`   ${index + 1}. ${proxy}`);
            });
            if (proxyList.length > 5) {
                console.log(`   ... und ${proxyList.length - 5} weitere`);
            }
        } else {
            console.log('   - Keine Proxys verfügbar');
        }

        console.log('\n✅ Test abgeschlossen!');
        console.log('\n📝 Nächste Schritte:');
        console.log('   - Starte den Bot mit: npm start');
        console.log('   - Verwende das Proxy-Tool mit: npm run proxy-tool');

    } catch (error) {
        console.error('❌ Fehler beim Testen:', error.message);
        console.log('\n🔧 Mögliche Lösungen:');
        console.log('   - Überprüfe die Internetverbindung');
        console.log('   - Versuche es später erneut');
        console.log('   - Kontaktiere den Support');
    }

    process.exit(0);
}

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Starte Test
testProxySystem();
