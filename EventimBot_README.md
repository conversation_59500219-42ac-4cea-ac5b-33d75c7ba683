# EventimBot - Wiederhergestellter Quellcode

## Beschreibung
EventimBot ist ein automatisierter Ticket-Buchungsbot für verschiedene Ticket-Plattformen.

## Kontakt
- **WhatsApp**: +212 622 056197
- **Email**: <EMAIL>


```
EventimBot/
├── eventimbot_main.js              ← HAUPTDATEI
├── package.json                    ← Korrekte Dependencies
├── package-lock.json               ← Neue Lock-Datei
├── EventimBot_README.md            ← Dokumentation
├── handlers/                       ← Website-Handler
│   ├── eventim.js                 ← Eventim.de Handler
│   ├── eventim-light.js           ← Eventim-Light.com Handler
│   ├── muenchenticket-de.js       ← MuenchenTicket.de Handler
│   └── muenchenticket-net.js      ← MuenchenTicket.net Handler
├── events/                         ← Event-Listen
│   ├── eventim.de.txt
│   ├── eventim-light.com.txt
│   ├── muenchenticket.de_tickets.txt
│   └── muenchenticket.net_shop.txt
├── browser/                        ← Chrome für Puppeteer
│   └── chrome.exe + Dateien
├── proxys.txt                      ← Proxy-Liste
└── proxy_auth.txt                  ← Proxy-Authentifizierung
```


## Unterstützte Websites
1. eventim.de
2. eventim-light.com
3. muenchenticket.de/tickets
4. muenchenticket.net/shop

## Installation
```bash
npm install
```

## Konfiguration
1. Erstelle `proxys.txt` mit einer Proxy-Liste (ein Proxy pro Zeile)
2. Erstelle `proxy_auth.txt` mit Proxy-Authentifizierung:
   ```
   username:your_username
   password:your_password
   ```
3. Erstelle Event-Listen in `./events/`:
   - `eventim.de.txt`
   - `eventim-light.com.txt`
   - `muenchenticket.de_tickets.txt`
   - `muenchenticket.net_shop.txt`

## Verwendung

### 1. Proxys scrapen (ZUERST ausführen)
```bash
npm run scrape-proxies
```

### 2. Bot starten
```bash
npm start
```

### 3. Proxy-Kompatibilität testen (optional)
```bash
npm run test-proxy-compatibility
```

## Struktur
- `index.js` - Hauptdatei
- `handlers/` - Website-spezifische Handler
  - `eventim.js` - Eventim.de Handler
  - `eventim-light.js` - Eventim-Light.com Handler
  - `muenchenticket-de.js` - MuenchenTicket.de Handler
  - `muenchenticket-net.js` - MuenchenTicket.net Handler

## Features
- Proxy-Rotation für IP-Wechsel
- Automatische Cookie-Banner-Entfernung
- Zufällige Event-Auswahl
- Konfigurierbare Ticket-Anzahl
- Wartezeiten zwischen Events
- Error-Handling für "Access Denied"

## Hinweise
- Der Bot verwendet Puppeteer mit Stealth-Plugin
- Chrome-Browser wird aus `./browser/chrome.exe` geladen
- Alle Handler sind modular aufgebaut


# 🎯 **Neu implementiert:**

Der EventimBot wurde vollständig von kostenpflichtigen Proxys auf ein manuelles Free Proxy System umgestellt.

## ⚠️ WICHTIGE ÄNDERUNG:
Das Proxy-System startet **NICHT** mehr automatisch! Proxys müssen manuell gescraped werden.

## 📋 **Was wurde geändert:**

### 🆕 **Neue Dateien:**
- `proxy-manager.js` - Manuelle Proxy-Verwaltung
- `proxy-tool.js` - Interaktives Proxy-Management-Tool
- `scrape-proxies.js` - **NEU:** Manueller Proxy-Scraper
- `test-proxy-compatibility.js` - **NEU:** Proxy-Kompatibilitäts-Tester
- `freeproxy-scraper.js` - FreeProxy.World Scraper
- `test-scraper.js` - Test für den Scraper
- `PROXY_README.md` - Detaillierte Dokumentation
- `demo-proxy-system.js` - Demo des Systems
- `test-proxy-system.js` - Test-Script

### 🔄 **Geänderte Dateien:**
- `eventimbot_main.js` - ProxyManager-Integration
- `handlers/*.js` - Entfernung der Proxy-Authentifizierung
- `package.json` - Neue Dependencies und Scripts

## 🆕 Neue Features

### Manuelles Proxy-Management
- **Manueller Proxy-Scraper** für kontrollierte Proxy-Beschaffung
- **Proxy-Kompatibilitäts-Tester** für Eventim-spezifische Tests
- **Proxy-Testing** vor der Verwendung
- **Intelligente Proxy-Rotation** mit defekten Proxy-Entfernung
- **Länder-spezifische Proxys** (DE, AT, CH)
- **Keine Authentifizierung** mehr erforderlich
- **Paralleles Proxy-Testing** für bessere Performance

## 📝 **Neuer Workflow:**

### Schritt 1: Proxys scrapen
```bash
npm run scrape-proxies
```
Dieses Tool scrapt Proxys von verschiedenen Quellen und speichert funktionierende Proxys in `proxys.txt`.

### Schritt 2: Proxy-Kompatibilität testen (empfohlen)
```bash
npm run test-proxy-compatibility
```
Testet die Proxys speziell mit Eventim-URLs und erstellt einen Kompatibilitäts-Report.

### Schritt 3: Bot starten
```bash
npm start
```
Der Bot lädt die Proxys aus `proxys.txt` und startet ohne Verzögerung.

### ⚠️ Wichtiger Hinweis
**Der Bot benötigt zwingend funktionierende Proxys!** Das Konzept basiert darauf, mit verschiedenen IP-Adressen mehrfach Tickets in den Warenkorb zu legen. Ohne Proxys kann der Bot nicht funktionieren.

### 🔍 **Proxy-Problem-Diagnose:**
Die Logs zeigen typische Proxy-Probleme mit HTTPS-Websites:
- `ERR_PROXY_CONNECTION_FAILED` - Proxy antwortet nicht
- `ERR_TUNNEL_CONNECTION_FAILED` - HTTPS-Tunnel kann nicht aufgebaut werden
- `ERR_CERT_AUTHORITY_INVALID` - Zertifikatsprobleme

Diese Probleme treten auf, weil viele Free Proxys nicht für HTTPS-Verbindungen geeignet sind.

### Proxy-Verwaltungstool
Ein separates Tool für die Proxy-Verwaltung: `proxy-tool.js`
Die alten Proxy-Konfigurationsdateien werden automatisch im `proxy_archiv/` Ordner gesichert.


### Tool starten
```bash
node proxy-tool.js
```

### Verfügbare Funktionen
1. **Proxy-Statistiken anzeigen** - Zeigt aktuelle Proxy-Anzahl und Status
2. **Proxys manuell aktualisieren** - Erzwingt eine sofortige Proxy-Aktualisierung
3. **Proxy-Liste anzeigen** - Zeigt alle verfügbaren Proxys
4. **Proxy testen** - Testet einzelne Proxys auf Funktionalität
5. **Defekten Proxy entfernen** - Entfernt nicht funktionierende Proxys
6. **Kontinuierliche Überwachung** - Überwacht Proxy-Status in Echtzeit

## 📁 Neue Dateien

### `proxy-manager.js`
Hauptklasse für die Proxy-Verwaltung:
- Automatisches Scraping von Proxys
- Proxy-Testing und -Validierung
- Proxy-Rotation und -Entfernung
- Backup-Funktionalität

### `proxy-tool.js`
Standalone-Tool für die Proxy-Verwaltung:
- Interaktive Benutzeroberfläche
- Manuelle Proxy-Kontrolle
- Monitoring-Funktionen

### Generierte Dateien
- `proxys.txt` - Aktuelle Proxy-Liste (automatisch generiert)
- `proxys_detailed.json` - Detaillierte Proxy-Informationen
- `proxy_archiv/` - Backup der alten Konfigurationen

## 🔄 Änderungen an bestehenden Dateien

### `eventimbot_main.js`
- ✅ ProxyManager-Integration
- ❌ Entfernung der Proxy-Authentifizierung
- ✅ Automatische Proxy-Initialisierung

### Handler-Dateien
Alle Handler (`handlers/*.js`) wurden aktualisiert:
- ✅ Verwendung des ProxyManagers
- ❌ Entfernung der `page.authenticate()` Aufrufe
- ✅ Automatische Proxy-Fehlerbehandlung

## 🌍 Proxy-Quellen

### Primäre Quelle: Proxifly API
- Hochwertige, getestete Proxys
- Länder-spezifische Filterung
- Anonymitäts-Level-Filterung

### Fallback: Direkte GitHub-Downloads
- Proxifly's GitHub Repository
- JSON-Format für detaillierte Informationen
- Backup bei API-Ausfällen

## ⚙️ Konfiguration

### Proxy-Filter-Einstellungen
```javascript
const options = {
    protocol: 'http',
    country: ['DE', 'AT', 'CH'],
    anonymity: ['anonymous', 'elite'],
    https: true,
    speed: 10000, // Max 10 Sekunden
    format: 'json'
};
```

### Aktualisierungsintervall
- **Standard**: 5 Minuten
- **Anpassbar** in `proxy-manager.js`

## 🛠️ Fehlerbehebung

### Keine Proxys verfügbar
```bash
node proxy-tool.js
# Option 2: Proxys manuell aktualisieren
```

### Proxy-Performance-Probleme
```bash
node proxy-tool.js
# Option 4: Proxy testen
# Option 5: Defekten Proxy entfernen
```

### Monitoring
```bash
node proxy-tool.js
# Option 6: Kontinuierliche Überwachung
```

## 📊 Proxy-Statistiken

Das System verfolgt automatisch:
- Anzahl funktionierender Proxys
- Letzte Aktualisierungszeit
- Verfügbare Länder
- Verwendete Protokolle
- Erfolgsrate der Proxy-Tests

## 🔒 Sicherheit

### Anonymität
- Nur anonyme und elite Proxys werden verwendet
- Automatische IP-Rotation
- Keine Logs der ursprünglichen IP

### Datenschutz
- Keine Speicherung von Benutzerdaten in Proxys
- Automatische Bereinigung defekter Proxys
- Lokale Proxy-Verwaltung

## 🚨 Wichtige Hinweise

1. **Free Proxys** können instabil sein - das System kompensiert dies durch automatische Rotation
2. **Geschwindigkeit** kann variieren - Proxys werden auf max. 10 Sekunden Antwortzeit getestet
3. **Verfügbarkeit** hängt von externen Quellen ab - mehrere Fallback-Mechanismen implementiert
