// EventimBot - Datenverbrauch-Optimierungskonfiguration
// Die<PERSON> definiert, welche Ressourcen blockiert werden sollen

const DataOptimizationConfig = {
    // Optimierungsstufen
    levels: {
        // Minimale Optimierung - nur offensichtlich unnötige Ressourcen blockieren
        minimal: {
            blockImages: false,
            blockFonts: true,
            blockAnalytics: true,
            blockAds: true,
            blockSocialMedia: true,
            blockVideos: true,
            allowCriticalCSS: true,
            allowCriticalJS: true
        },
        
        // Moderate Optimierung - Balance zwischen Datensparen und Funktionalität
        moderate: {
            blockImages: true,
            blockFonts: true,
            blockAnalytics: true,
            blockAds: true,
            blockSocialMedia: true,
            blockVideos: true,
            allowCriticalCSS: true,
            allowCriticalJS: true,
            allowEventimImages: true // Kritische Eventim-Bilder erlauben
        },
        
        // Aggressive Optimierung - Maximale Dateneinsparung
        aggressive: {
            blockImages: true,
            blockFonts: true,
            blockAnalytics: true,
            blockAds: true,
            blockSocialMedia: true,
            blockVideos: true,
            allowCriticalCSS: false,
            allowCriticalJS: true,
            allowEventimImages: false,
            blockNonEssentialCSS: true
        }
    },

    // Standard-Optimierungsstufe
    defaultLevel: 'moderate',

    // Ressourcen-Kategorien und ihre URL-Patterns
    resourcePatterns: {
        // Bilder
        images: [
            /\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)(\?.*)?$/i,
            /\/images?\//i,
            /\/img\//i,
            /\/assets\/.*\.(jpg|jpeg|png|gif|bmp|webp|svg|ico)/i
        ],

        // Fonts
        fonts: [
            /\.(woff|woff2|ttf|eot|otf)(\?.*)?$/i,
            /\/fonts?\//i,
            /fonts\.googleapis\.com/i,
            /fonts\.gstatic\.com/i,
            /typekit\.net/i
        ],

        // Analytics & Tracking
        analytics: [
            /google-analytics\.com/i,
            /googletagmanager\.com/i,
            /doubleclick\.net/i,
            /facebook\.net/i,
            /facebook\.com\/tr/i,
            /hotjar\.com/i,
            /mixpanel\.com/i,
            /segment\.com/i,
            /amplitude\.com/i,
            /analytics/i,
            /tracking/i,
            /gtm\.js/i,
            /ga\.js/i
        ],

        // Werbung
        ads: [
            /googlesyndication\.com/i,
            /googleadservices\.com/i,
            /adsystem\.com/i,
            /amazon-adsystem\.com/i,
            /adsense/i,
            /adnxs\.com/i,
            /adsystem/i,
            /ads\./i,
            /\/ads\//i
        ],

        // Social Media
        socialMedia: [
            /facebook\.com\/plugins/i,
            /twitter\.com\/widgets/i,
            /instagram\.com\/embed/i,
            /youtube\.com\/embed/i,
            /linkedin\.com\/widgets/i,
            /pinterest\.com\/widgets/i,
            /social/i
        ],

        // Videos
        videos: [
            /\.(mp4|avi|mov|wmv|flv|webm|mkv)(\?.*)?$/i,
            /youtube\.com\/watch/i,
            /vimeo\.com/i,
            /video/i
        ],

        // Kritische Eventim-Ressourcen (NICHT blockieren)
        eventimCritical: [
            /eventim\.(de|com)/i,
            /muenchenticket\.(de|net)/i,
            /\/api\//i,
            /\/checkout/i,
            /\/cart/i,
            /\/ticket/i,
            /\/event/i,
            /\/booking/i
        ],

        // Kritische JavaScript-Bibliotheken (NICHT blockieren)
        criticalJS: [
            /jquery/i,
            /bootstrap/i,
            /angular/i,
            /react/i,
            /vue/i,
            /\/js\/.*\.(min\.)?js$/i
        ],

        // Kritische CSS (NICHT blockieren bei moderate/minimal)
        criticalCSS: [
            /bootstrap/i,
            /main\.css/i,
            /style\.css/i,
            /app\.css/i,
            /\/css\/.*\.css$/i
        ]
    },

    // Browser-Launch-Optimierungen
    browserOptimizations: {
        // Chrome-Flags für Datensparen
        chromeFlags: [
            '--disable-background-timer-throttling',
            '--disable-backgrounding-occluded-windows',
            '--disable-renderer-backgrounding',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-background-networking',
            '--disable-sync',
            '--disable-default-apps',
            '--disable-extensions',
            '--disable-plugins',
            '--disable-preconnect',
            '--disable-prefetch',
            '--no-pings',
            '--disable-client-side-phishing-detection',
            '--disable-component-update',
            '--disable-domain-reliability'
        ],

        // Zusätzliche Flags für aggressive Optimierung
        aggressiveFlags: [
            '--disable-images',
            '--disable-javascript-harmony-shipping',
            '--disable-reading-from-canvas',
            '--disable-accelerated-2d-canvas',
            '--disable-accelerated-jpeg-decoding',
            '--disable-accelerated-mjpeg-decode',
            '--disable-accelerated-video-decode'
        ]
    },

    // Eventim-spezifische Optimierungen
    eventimOptimizations: {
        // Erlaubte Eventim-Domains für Bilder
        allowedImageDomains: [
            'static.eventim.com',
            'images.eventim.com',
            'cdn.eventim.com'
        ],

        // Kritische Selektoren die Bilder benötigen könnten
        criticalImageSelectors: [
            '.event-image',
            '.venue-map',
            '.seating-chart',
            '.ticket-image'
        ]
    }
};

module.exports = DataOptimizationConfig;
