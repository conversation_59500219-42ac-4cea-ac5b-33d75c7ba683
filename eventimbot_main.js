
const puppeteerExtra = require('puppeteer-extra').use(require('puppeteer-extra-plugin-stealth')());
const readlineSync = require('readline-sync');
const ProxyManager = require('./proxy-manager');
const ResourceBlocker = require('./resource-blocker');
const DataOptimizationConfig = require('./data-optimization-config');

// Globale Variablen
let proxyManager;
let errorMessages = ['HTTP ERROR', 'Access Denied'];
let dataOptimizationLevel = 'moderate'; // Standard-Optimierungslevel

// Initialisiere Proxy-Manager (ohne automatisches Laden)
function initProxyManager() {
    console.log('🚀 Initialisiere Free Proxy Manager...');
    proxyManager = new ProxyManager(false); // false = kein automatisches Laden
    console.log('✅ Proxy Manager gestartet (manueller Modus)');
}

// Legacy-Funktion für Rückwärtskompatibilität
function loadConfig() {
    // Diese Funktion wird nicht mehr benötigt, da der ProxyManager
    // automatisch die Proxys verwaltet
    console.log('ℹ️ Proxy-Konfiguration wird automatisch vom ProxyManager verwaltet');
}

// Hilfsfunktionen
async function sleep(seconds) {
    return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function clickElement(page, selector) {
    const element = await page.$(selector);
    await element.click().catch(error => console.log(error));
}

async function launchBrowser(proxy, optimizationLevel = null) {
    const level = optimizationLevel || dataOptimizationLevel;
    const config = DataOptimizationConfig;

    console.log(`🚀 Starte Browser mit Datenoptimierung (Level: ${level})...`);

    // Basis Chrome-Flags
    let chromeArgs = [
        '--no-first-run',
        '--proxy-server=' + proxy,
        '--no-sandbox',
        '--disable-setuid-sandbox'
    ];

    // Füge Datenverbrauch-Optimierungen hinzu
    chromeArgs = chromeArgs.concat(config.browserOptimizations.chromeFlags);

    // Bei aggressiver Optimierung zusätzliche Flags
    if (level === 'aggressive') {
        chromeArgs = chromeArgs.concat(config.browserOptimizations.aggressiveFlags);
    }

    return await puppeteerExtra.launch({
        // Verwende System-Chrome/Chromium (entferne executablePath für automatische Erkennung)
        'headless': false,
        'defaultViewport': null,
        'ignoreHTTPSErrors': true,
        'timeout': 60000,
        'args': chromeArgs
    });
}

// Neue Funktion: Erstelle optimierte Page mit Resource-Blocking
async function createOptimizedPage(browser, optimizationLevel = null) {
    const level = optimizationLevel || dataOptimizationLevel;
    const page = await browser.newPage();

    // Initialisiere Resource-Blocker
    const resourceBlocker = new ResourceBlocker(level);
    await resourceBlocker.setupResourceBlocking(page);

    // Speichere Resource-Blocker in Page für spätere Statistiken
    page._resourceBlocker = resourceBlocker;

    return page;
}



// Funktion zum Setzen des Optimierungslevels
function setDataOptimizationLevel(level) {
    if (DataOptimizationConfig.levels[level]) {
        dataOptimizationLevel = level;
        console.log(`🔧 Datenoptimierung geändert zu: ${level}`);
    } else {
        console.log(`❌ Unbekanntes Optimierungslevel: ${level}`);
        console.log(`Verfügbare Level: ${Object.keys(DataOptimizationConfig.levels).join(', ')}`);
    }
}

// Exportiere Hilfsfunktionen für Handler
module.exports = {
    sleep,
    clickElement,
    launchBrowser,
    createOptimizedPage,
    setDataOptimizationLevel,
    get proxyManager() { return proxyManager; },
    get errorMessages() { return errorMessages; },
    get dataOptimizationLevel() { return dataOptimizationLevel; }
};

// Website-Handler werden nach ProxyManager-Initialisierung geladen
let EventimHandler, EventimLightHandler, MuenchenTicketDEHandler, MuenchenTicketNETHandler;

function loadHandlers() {
    EventimHandler = require('./handlers/eventim');
    EventimLightHandler = require('./handlers/eventim-light');
    MuenchenTicketDEHandler = require('./handlers/muenchenticket-de');
    MuenchenTicketNETHandler = require('./handlers/muenchenticket-net');
}

async function main() {
    // Initialisiere Proxy-Manager
    initProxyManager();

    // Lade Handler nach ProxyManager-Initialisierung
    loadHandlers();

    // Lade vorhandene Proxys aus proxys.txt (falls vorhanden)
    console.log('📂 Lade vorhandene Proxy-Liste...');
    proxyManager.loadProxiesFromFile();

    console.log(`
    Contact Me
    ¨¨¨¨¨¨¨¨¨¨
Whatsapp : +212 622 056197
Email    : <EMAIL>

`);

    console.log(`
1 : eventim.de
2 : eventim-light.com
3 : muenchenticket.de/tickets
4 : muenchenticket.net/shop`);

    let websiteChoice = null;

    while (true) {
        websiteChoice = parseInt(readlineSync.question('\nPlease enter the website number and hit enter: '));

        if (!isNaN(websiteChoice)) {
            switch (websiteChoice) {
                case 1:
                    await EventimHandler.run();
                    break;
                case 2:
                    await EventimLightHandler.run();
                    break;
                case 3:
                    await MuenchenTicketDEHandler.run();
                    break;
                case 4:
                    await MuenchenTicketNETHandler.run();
                    break;
                default:
                    console.log('Number not found');
                    continue;
            }
            break;
        } else {
            console.log('Enter a valid number. Please try again.');
        }
    }
}

// Starte Bot wenn direkt ausgeführt
if (require.main === module) {
    main();
}
