#!/usr/bin/env node

const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const fs = require('fs');

puppeteer.use(StealthPlugin());

class ProxyCompatibilityTester {
    constructor() {
        this.testUrls = [
            // Basis-Tests
            { name: 'HTTP Test', url: 'http://httpbin.org/ip', protocol: 'http' },
            { name: 'HTTPS Test', url: 'https://httpbin.org/ip', protocol: 'https' },
            
            // Deutsche Websites
            { name: 'Google.de', url: 'https://www.google.de', protocol: 'https' },
            { name: 'Heise.de', url: 'https://www.heise.de', protocol: 'https' },
            
            // Eventim-spezifische Tests
            { name: 'Eventim.de Hauptseite', url: 'https://www.eventim.de', protocol: 'https' },
            { name: 'Eventim.de Event', url: 'https://www.eventim.de/event/stand-up-show-im-lucky-punch-showcase-lucky-punch-comedy-club-20244715/', protocol: 'https' },
            
            // Andere Ticket-Websites
            { name: 'Eventim-Light', url: 'https://www.eventim-light.com', protocol: 'https' },
            { name: 'MuenchenTicket.de', url: 'https://muenchenticket.de', protocol: 'https' }
        ];
        
        this.proxies = this.loadProxies();
    }

    loadProxies() {
        try {
            if (fs.existsSync('proxys.txt')) {
                const content = fs.readFileSync('proxys.txt', 'utf8');
                return content.split('\n').filter(line => line.trim());
            } else {
                console.log('❌ Keine proxys.txt gefunden. Führe zuerst "node scrape-proxies.js" aus.');
                return [];
            }
        } catch (error) {
            console.log(`❌ Fehler beim Laden der Proxys: ${error.message}`);
            return [];
        }
    }

    async testProxyWithUrl(proxy, testUrl) {
        let browser = null;
        try {
            console.log(`🔍 Teste ${proxy} mit ${testUrl.name}...`);
            
            browser = await puppeteer.launch({
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--proxy-server=' + proxy,
                    '--ignore-certificate-errors',
                    '--ignore-ssl-errors',
                    '--ignore-certificate-errors-spki-list',
                    '--disable-web-security'
                ],
                timeout: 30000
            });

            const page = await browser.newPage();
            
            // Setze Timeouts
            page.setDefaultTimeout(15000);
            page.setDefaultNavigationTimeout(15000);

            // Navigiere zur URL
            const response = await page.goto(testUrl.url, {
                waitUntil: 'domcontentloaded',
                timeout: 15000
            });

            const status = response.status();
            const success = status >= 200 && status < 400;

            if (success) {
                // Zusätzliche Checks für Eventim
                if (testUrl.url.includes('eventim')) {
                    const content = await page.content();
                    
                    // Prüfe auf bekannte Fehlerseiten
                    if (content.includes('Access Denied') || 
                        content.includes('HTTP ERROR') ||
                        content.includes('Cloudflare') ||
                        content.includes('Just a moment')) {
                        return { success: false, status, error: 'Blocked by website' };
                    }
                }

                return { success: true, status, error: null };
            } else {
                return { success: false, status, error: `HTTP ${status}` };
            }

        } catch (error) {
            let errorType = 'Unknown error';
            
            if (error.message.includes('ERR_PROXY_CONNECTION_FAILED')) {
                errorType = 'Proxy connection failed';
            } else if (error.message.includes('ERR_TUNNEL_CONNECTION_FAILED')) {
                errorType = 'Tunnel connection failed';
            } else if (error.message.includes('ERR_CERT_AUTHORITY_INVALID')) {
                errorType = 'Certificate error';
            } else if (error.message.includes('timeout')) {
                errorType = 'Timeout';
            }

            return { success: false, status: null, error: errorType };
        } finally {
            if (browser) {
                await browser.close().catch(() => {});
            }
        }
    }

    async testAllProxies() {
        if (this.proxies.length === 0) {
            console.log('❌ Keine Proxys zum Testen verfügbar');
            return;
        }

        console.log(`🧪 Teste ${this.proxies.length} Proxys mit ${this.testUrls.length} URLs...`);
        console.log('='.repeat(80));

        const results = {};
        
        for (const proxy of this.proxies) {
            console.log(`\n🔍 Teste Proxy: ${proxy}`);
            results[proxy] = {};
            
            for (const testUrl of this.testUrls) {
                const result = await this.testProxyWithUrl(proxy, testUrl);
                results[proxy][testUrl.name] = result;
                
                const status = result.success ? '✅' : '❌';
                const details = result.success ? `(${result.status})` : `(${result.error})`;
                console.log(`  ${status} ${testUrl.name} ${details}`);
            }
            
            // Kurze Pause zwischen Proxys
            await this.sleep(1000);
        }

        this.generateReport(results);
    }

    generateReport(results) {
        console.log('\n📊 ZUSAMMENFASSUNG');
        console.log('='.repeat(80));

        const proxyScores = {};
        
        // Berechne Erfolgsrate für jeden Proxy
        for (const [proxy, tests] of Object.entries(results)) {
            const totalTests = Object.keys(tests).length;
            const successfulTests = Object.values(tests).filter(test => test.success).length;
            const successRate = (successfulTests / totalTests * 100).toFixed(1);
            
            proxyScores[proxy] = {
                successRate: parseFloat(successRate),
                successful: successfulTests,
                total: totalTests,
                tests: tests
            };
        }

        // Sortiere Proxys nach Erfolgsrate
        const sortedProxies = Object.entries(proxyScores)
            .sort(([,a], [,b]) => b.successRate - a.successRate);

        console.log('\n🏆 Proxy-Ranking (nach Erfolgsrate):');
        sortedProxies.forEach(([proxy, score], index) => {
            const rank = index + 1;
            const eventimTests = Object.entries(score.tests)
                .filter(([name]) => name.includes('Eventim'))
                .map(([name, result]) => result.success ? '✅' : '❌')
                .join(' ');
            
            console.log(`${rank}. ${proxy} - ${score.successRate}% (${score.successful}/${score.total}) | Eventim: ${eventimTests}`);
        });

        // Spezielle Eventim-Analyse
        console.log('\n🎫 Eventim-Kompatibilität:');
        const eventimCompatible = sortedProxies.filter(([proxy, score]) => {
            const eventimTests = Object.entries(score.tests)
                .filter(([name]) => name.includes('Eventim'));
            return eventimTests.some(([, result]) => result.success);
        });

        if (eventimCompatible.length > 0) {
            console.log('✅ Eventim-kompatible Proxys:');
            eventimCompatible.forEach(([proxy]) => {
                console.log(`  - ${proxy}`);
            });
            
            // Speichere die besten Proxys
            const bestProxies = eventimCompatible.slice(0, 5).map(([proxy]) => proxy);
            this.saveBestProxies(bestProxies);
        } else {
            console.log('❌ Keine Eventim-kompatiblen Proxys gefunden!');
            console.log('\n💡 Empfehlungen:');
            console.log('1. Versuche andere Proxy-Quellen');
            console.log('2. Verwende kostenpflichtige Proxys');
            console.log('3. Teste zu anderen Zeiten (weniger Blockierungen)');
        }

        // Häufigste Fehler
        console.log('\n🔍 Häufigste Fehler:');
        const errorCounts = {};
        Object.values(results).forEach(tests => {
            Object.values(tests).forEach(test => {
                if (!test.success && test.error) {
                    errorCounts[test.error] = (errorCounts[test.error] || 0) + 1;
                }
            });
        });

        Object.entries(errorCounts)
            .sort(([,a], [,b]) => b - a)
            .forEach(([error, count]) => {
                console.log(`  ${error}: ${count}x`);
            });
    }

    saveBestProxies(bestProxies) {
        try {
            fs.writeFileSync('proxys_best.txt', bestProxies.join('\n'));
            console.log(`\n💾 ${bestProxies.length} beste Proxys in proxys_best.txt gespeichert`);
        } catch (error) {
            console.log(`❌ Fehler beim Speichern: ${error.message}`);
        }
    }

    async testSingleProxy() {
        if (this.proxies.length === 0) {
            console.log('❌ Keine Proxys verfügbar');
            return;
        }

        console.log('\n📋 Verfügbare Proxys:');
        this.proxies.forEach((proxy, index) => {
            console.log(`${index + 1}. ${proxy}`);
        });

        const readlineSync = require('readline-sync');
        const choice = readlineSync.question('\nWähle einen Proxy (Nummer): ');
        const proxyIndex = parseInt(choice) - 1;

        if (proxyIndex >= 0 && proxyIndex < this.proxies.length) {
            const proxy = this.proxies[proxyIndex];
            console.log(`\n🔍 Teste Proxy: ${proxy}`);
            
            for (const testUrl of this.testUrls) {
                const result = await this.testProxyWithUrl(proxy, testUrl);
                const status = result.success ? '✅' : '❌';
                const details = result.success ? `(${result.status})` : `(${result.error})`;
                console.log(`${status} ${testUrl.name} ${details}`);
            }
        } else {
            console.log('❌ Ungültige Auswahl');
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async showMenu() {
        const readlineSync = require('readline-sync');
        
        console.log('\n🧪 Proxy-Kompatibilitäts-Tester');
        console.log('================================');
        console.log('1. Alle Proxys testen');
        console.log('2. Einzelnen Proxy testen');
        console.log('3. Nur Eventim-URLs testen');
        console.log('0. Beenden');
        console.log('================================');

        const choice = readlineSync.question('Wählen Sie eine Option: ');
        
        switch (choice) {
            case '1':
                await this.testAllProxies();
                break;
            case '2':
                await this.testSingleProxy();
                break;
            case '3':
                await this.testEventimOnly();
                break;
            case '0':
                console.log('👋 Auf Wiedersehen!');
                process.exit(0);
                break;
            default:
                console.log('❌ Ungültige Auswahl');
        }

        await this.sleep(2000);
        await this.showMenu();
    }

    async testEventimOnly() {
        const eventimUrls = this.testUrls.filter(url => url.name.includes('Eventim'));
        
        if (this.proxies.length === 0) {
            console.log('❌ Keine Proxys verfügbar');
            return;
        }

        console.log(`🎫 Teste ${this.proxies.length} Proxys nur mit Eventim-URLs...`);
        
        for (const proxy of this.proxies) {
            console.log(`\n🔍 Teste Proxy: ${proxy}`);
            
            for (const testUrl of eventimUrls) {
                const result = await this.testProxyWithUrl(proxy, testUrl);
                const status = result.success ? '✅' : '❌';
                const details = result.success ? `(${result.status})` : `(${result.error})`;
                console.log(`  ${status} ${testUrl.name} ${details}`);
            }
        }
    }
}

// Starte das Tool
async function main() {
    console.log('🚀 Proxy-Kompatibilitäts-Tester gestartet');
    console.log('==========================================');
    console.log('Dieses Tool testet Proxy-Kompatibilität mit verschiedenen Websites');
    console.log('einschließlich Eventim.de\n');

    const tester = new ProxyCompatibilityTester();
    await tester.showMenu();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ProxyCompatibilityTester;
