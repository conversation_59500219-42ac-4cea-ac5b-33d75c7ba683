# EventimBot Proxy-Workflow

## 🚨 WICHTIGE ÄNDERUNG
Das automatische Proxy-System wurde deaktiviert! Proxys müssen jetzt manuell gescraped werden.

## 📋 Neuer Workflow

### 1. Proxys scrapen
```bash
npm run scrape-proxies
```

**Was passiert:**
- <PERSON>rapt Proxy<PERSON> von ve<PERSON>llen (Proxifly, FreeProxy.World)
- Testet die Proxys auf Funktionalität
- Speichert funktionierende Proxys in `proxys.txt`

**Optionen im Tool:**
1. Proxys von allen Quellen scrapen (empfohlen)
2. Nur FreeProxy.World scrapen
3. Nur Proxifly API verwenden
4. Aktuelle Proxy-Liste anzeigen
5. Proxy-Test durchführen

### 2. Proxy-Kompatibilität testen (empfohlen)
```bash
npm run test-proxy-compatibility
```
**WICHTIG:** Befehl ohne `.js` am Ende!

**Was passiert:**
- Testet alle Proxys aus `proxys.txt` mit verschiedenen URLs
- Spezieller Test für Eventim-URLs
- Erstellt detaillierten Kompatibilitäts-Report
- Speichert beste Proxys in `proxys_best.txt`

**Test-URLs:**
- HTTP/HTTPS Basis-Tests
- Google.de, Heise.de
- Eventim.de Hauptseite und Event-URLs
- Eventim-Light, MuenchenTicket

### 3. Bot starten
```bash
npm start
```

**Was passiert:**
- Lädt Proxys aus `proxys.txt`
- Startet sofort ohne Verzögerung
- Verwendet die vorhandenen Proxys für Rotation

## 🔍 Problem-Diagnose

### Häufige Proxy-Fehler:
- `ERR_PROXY_CONNECTION_FAILED` - Proxy antwortet nicht
- `ERR_TUNNEL_CONNECTION_FAILED` - HTTPS-Tunnel Probleme
- `ERR_CERT_AUTHORITY_INVALID` - Zertifikatsfehler

### Lösungsansätze:
1. **Mehr Proxys scrapen**: Verwende Option 1 im Scraper
2. **Bessere Proxys finden**: Teste zu verschiedenen Zeiten
3. **Proxy-Kompatibilität prüfen**: Nutze den Kompatibilitäts-Tester
4. **Kostenpflichtige Proxys**: Für bessere Stabilität

## 🛠️ Zusätzliche Tools

### Proxy-Tool (erweitert)
```bash
npm run proxy-tool
```
Erweiterte Proxy-Verwaltung mit mehr Optionen.

### Einzelne Proxy-Tests
```bash
npm run test-proxy-compatibility
```
Dann Option 2 wählen für einzelne Proxy-Tests.

## 📊 Erfolgs-Tipps

1. **Scrape regelmäßig**: Free Proxys ändern sich häufig
2. **Teste vor dem Bot-Start**: Verwende den Kompatibilitäts-Tester
3. **Verwende die besten Proxys**: Kopiere `proxys_best.txt` zu `proxys.txt`
4. **Teste zu verschiedenen Zeiten**: Proxy-Verfügbarkeit variiert

## 🎯 Eventim-spezifische Tipps

Eventim blockiert viele Free Proxys. Der Kompatibilitäts-Tester hilft dabei:
- Eventim-kompatible Proxys zu finden
- Häufige Fehlertypen zu identifizieren
- Die besten Proxys zu priorisieren

Wenn keine Eventim-kompatiblen Proxys gefunden werden:
1. Zu anderen Zeiten scrapen
2. Andere Proxy-Quellen verwenden
3. Kostenpflichtige Proxys in Betracht ziehen
