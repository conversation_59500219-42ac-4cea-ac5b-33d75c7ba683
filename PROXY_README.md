# EventimBot - Free Proxy Integration

## Übersicht
Der EventimBot wurde erfolgreich auf die Verwendung von kostenlosen Proxys umgestellt. Das System verwendet jetzt **Proxifly** um automatisch Proxys aus Deutschland, Österreich und der Schweiz zu be<PERSON>, zu testen und zu verwalten.

## 🆕 Neue Features

### Automatisches Proxy-Management
- **Automatische Proxy-Beschaffung** alle 5 Minuten
- **Proxy-Testing** vor der Verwendung
- **Intelligente Proxy-Rotation** mit defekten Proxy-Entfernung
- **Länder-spezifische Proxys** (DE, AT, CH)
- **Keine Authentifizierung** mehr erforderlich
- **Paralleles Proxy-Testing** für bessere Performance

### ⚠️ Wichtiger Hinweis
**Der Bot benötigt zwingend funktionierende Proxys!** Das Konzept basiert darauf, mit verschiedenen IP-Adressen mehrfach Tickets in den Warenkorb zu legen. Ohne Proxys kann der Bot nicht funktionieren.

### Proxy-Verwaltungstool
Ein separates Tool für die Proxy-Verwaltung: `proxy-tool.js`

## 🚀 Installation und Setup

### 1. Dependencies installieren
```bash
npm install proxifly axios
```

### 2. Alte Proxy-Konfiguration sichern
Die alten Proxy-Konfigurationsdateien werden automatisch im `proxy_archiv/` Ordner gesichert.

### 3. Bot starten
```bash
npm start
```

## 🔧 Proxy-Verwaltungstool

### Tool starten
```bash
node proxy-tool.js
```

### Verfügbare Funktionen
1. **Proxy-Statistiken anzeigen** - Zeigt aktuelle Proxy-Anzahl und Status
2. **Proxys manuell aktualisieren** - Erzwingt eine sofortige Proxy-Aktualisierung
3. **Proxy-Liste anzeigen** - Zeigt alle verfügbaren Proxys
4. **Proxy testen** - Testet einzelne Proxys auf Funktionalität
5. **Defekten Proxy entfernen** - Entfernt nicht funktionierende Proxys
6. **Kontinuierliche Überwachung** - Überwacht Proxy-Status in Echtzeit

## 📁 Neue Dateien

### `proxy-manager.js`
Hauptklasse für die Proxy-Verwaltung:
- Automatisches Scraping von Proxys
- Proxy-Testing und -Validierung
- Proxy-Rotation und -Entfernung
- Backup-Funktionalität

### `proxy-tool.js`
Standalone-Tool für die Proxy-Verwaltung:
- Interaktive Benutzeroberfläche
- Manuelle Proxy-Kontrolle
- Monitoring-Funktionen

### Generierte Dateien
- `proxys.txt` - Aktuelle Proxy-Liste (automatisch generiert)
- `proxys_detailed.json` - Detaillierte Proxy-Informationen
- `proxy_archiv/` - Backup der alten Konfigurationen

## 🔄 Änderungen an bestehenden Dateien

### `eventimbot_main.js`
- ✅ ProxyManager-Integration
- ❌ Entfernung der Proxy-Authentifizierung
- ✅ Automatische Proxy-Initialisierung

### Handler-Dateien
Alle Handler (`handlers/*.js`) wurden aktualisiert:
- ✅ Verwendung des ProxyManagers
- ❌ Entfernung der `page.authenticate()` Aufrufe
- ✅ Automatische Proxy-Fehlerbehandlung

## 🌍 Proxy-Quellen

### Primäre Quelle: Proxifly API
- Hochwertige, getestete Proxys
- Länder-spezifische Filterung
- Anonymitäts-Level-Filterung

### Fallback: Direkte GitHub-Downloads
- Proxifly's GitHub Repository
- JSON-Format für detaillierte Informationen
- Backup bei API-Ausfällen

## ⚙️ Konfiguration

### Proxy-Filter-Einstellungen
```javascript
const options = {
    protocol: 'http',
    country: ['DE', 'AT', 'CH'],
    anonymity: ['anonymous', 'elite'],
    https: true,
    speed: 10000, // Max 10 Sekunden
    format: 'json'
};
```

### Aktualisierungsintervall
- **Standard**: 5 Minuten
- **Anpassbar** in `proxy-manager.js`

## 🛠️ Fehlerbehebung

### Keine Proxys verfügbar
```bash
node proxy-tool.js
# Option 2: Proxys manuell aktualisieren
```

### Proxy-Performance-Probleme
```bash
node proxy-tool.js
# Option 4: Proxy testen
# Option 5: Defekten Proxy entfernen
```

### Monitoring
```bash
node proxy-tool.js
# Option 6: Kontinuierliche Überwachung
```

## 📊 Proxy-Statistiken

Das System verfolgt automatisch:
- Anzahl funktionierender Proxys
- Letzte Aktualisierungszeit
- Verfügbare Länder
- Verwendete Protokolle
- Erfolgsrate der Proxy-Tests

## 🔒 Sicherheit

### Anonymität
- Nur anonyme und elite Proxys werden verwendet
- Automatische IP-Rotation
- Keine Logs der ursprünglichen IP

### Datenschutz
- Keine Speicherung von Benutzerdaten in Proxys
- Automatische Bereinigung defekter Proxys
- Lokale Proxy-Verwaltung

## 🚨 Wichtige Hinweise

1. **Free Proxys** können instabil sein - das System kompensiert dies durch automatische Rotation
2. **Geschwindigkeit** kann variieren - Proxys werden auf max. 10 Sekunden Antwortzeit getestet
3. **Verfügbarkeit** hängt von externen Quellen ab - mehrere Fallback-Mechanismen implementiert

## 📞 Support

Bei Problemen mit der Proxy-Integration:
1. Überprüfen Sie die Proxy-Statistiken mit `node proxy-tool.js`
2. Erzwingen Sie eine manuelle Aktualisierung
3. Kontaktieren Sie den Support bei anhaltenden Problemen

---

**Hinweis**: Diese Umstellung macht den Bot vollständig unabhängig von kostenpflichtigen Proxy-Services und verbessert die Zuverlässigkeit durch automatische Proxy-Verwaltung.
