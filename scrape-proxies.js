#!/usr/bin/env node

const ProxyManager = require('./proxy-manager');
const readlineSync = require('readline-sync');

class ProxyScraper {
    constructor() {
        this.proxyManager = new ProxyManager(false); // Kein automatisches Laden
    }

    async showMenu() {
        console.log('\n🕷️ EventimBot Proxy-Scraper');
        console.log('============================');
        console.log('1. Proxys von allen Quellen scrapen');
        console.log('2. Nur FreeProxy.World scrapen');
        console.log('3. Nur Proxifly API verwenden');
        console.log('4. Aktuelle Proxy-Liste anzeigen');
        console.log('5. Proxy-Test durchführen');
        console.log('0. Beenden');
        console.log('============================');

        const choice = readlineSync.question('Wählen Sie eine Option: ');
        await this.handleChoice(choice);
    }

    async handleChoice(choice) {
        switch (choice) {
            case '1':
                await this.scrapeAllSources();
                break;
            case '2':
                await this.scrapeFreeProxyWorld();
                break;
            case '3':
                await this.scrapeProxifly();
                break;
            case '4':
                this.showProxyList();
                break;
            case '5':
                await this.testProxies();
                break;
            case '0':
                console.log('👋 Auf Wiedersehen!');
                process.exit(0);
                break;
            default:
                console.log('❌ Ungültige Auswahl');
        }

        // Zurück zum Menü
        await this.sleep(2000);
        await this.showMenu();
    }

    async scrapeAllSources() {
        console.log('\n🔄 Starte Proxy-Scraping von allen Quellen...');

        // Sammle Proxys von allen Quellen
        const allProxies = [];

        // 1. FreeProxy.World
        console.log('1. FreeProxy.World scrapen...');
        const freeProxies = await this.proxyManager.scrapeFreeProxyWorld();
        allProxies.push(...freeProxies);

        // 2. Proxifly
        console.log('2. Proxifly API verwenden...');
        const countries = ['DE', 'AT', 'CH'];
        for (const country of countries) {
            const proxies = await this.proxyManager.fetchProxiesFromDirectURL(country);
            allProxies.push(...proxies);
        }

        if (allProxies.length > 0) {
            this.saveAllProxies(allProxies);
            console.log(`✅ ${allProxies.length} Proxys von allen Quellen gescraped und gespeichert`);
            console.log('📝 Verwende Option 5 "Proxy-Test durchführen" um die Proxys zu testen');
        } else {
            console.log('❌ Keine Proxys von allen Quellen gefunden');
        }
    }

    async scrapeFreeProxyWorld() {
        console.log('\n🕷️ Starte FreeProxy.World Scraping...');
        const proxies = await this.proxyManager.scrapeFreeProxyWorld();

        if (proxies.length > 0) {
            // Speichere ALLE gefundenen Proxys ungefiltert
            this.saveAllProxies(proxies);
            console.log(`✅ ${proxies.length} Proxys gescraped und in proxys_all.txt gespeichert`);
            console.log('📝 Verwende Option 5 "Proxy-Test durchführen" um die Proxys zu testen');
        } else {
            console.log('❌ Keine Proxys gefunden');
        }
    }

    async scrapeProxifly() {
        console.log('\n📡 Starte Proxifly API Scraping...');
        const countries = ['DE', 'AT', 'CH'];
        const allProxies = [];

        for (const country of countries) {
            console.log(`📡 Lade Proxys aus ${country}...`);
            const proxies = await this.proxyManager.fetchProxiesFromDirectURL(country);
            allProxies.push(...proxies);
        }

        if (allProxies.length > 0) {
            // Speichere ALLE gefundenen Proxys ungefiltert
            this.saveAllProxies(allProxies);
            console.log(`✅ ${allProxies.length} Proxys gescraped und in proxys_all.txt gespeichert`);
            console.log('📝 Verwende Option 5 "Proxy-Test durchführen" um die Proxys zu testen');
        } else {
            console.log('❌ Keine Proxys gefunden');
        }
    }

    showProxyList() {
        console.log('\n📋 Proxy-Listen Übersicht:');

        // Zeige gescrapte Proxys (ungefiltert)
        const allProxies = this.loadAllProxies();
        console.log(`\n🕷️ Gescrapte Proxys (proxys_all.txt): ${allProxies.length}`);
        if (allProxies.length > 0) {
            console.log('Erste 10 Proxys:');
            allProxies.slice(0, 10).forEach((proxy, index) => {
                console.log(`  ${index + 1}. ${proxy.ip}:${proxy.port}`);
            });
            if (allProxies.length > 10) {
                console.log(`  ... und ${allProxies.length - 10} weitere`);
            }
        }

        // Zeige getestete Proxys (funktionierend)
        const workingProxies = this.proxyManager.getProxyList();
        console.log(`\n✅ Getestete Proxys (proxys.txt): ${workingProxies.length}`);
        if (workingProxies.length > 0) {
            workingProxies.forEach((proxy, index) => {
                console.log(`  ${index + 1}. ${proxy}`);
            });
        }

        if (allProxies.length === 0 && workingProxies.length === 0) {
            console.log('❌ Keine Proxys verfügbar');
            console.log('📝 Verwende zuerst eine der Scraping-Optionen (1-3)');
        }
    }

    async testProxies() {
        console.log('\n🧪 Teste alle verfügbaren Proxys...');

        // Lade Proxys aus proxys_all.txt
        const allProxies = this.loadAllProxies();
        if (allProxies.length === 0) {
            console.log('❌ Keine Proxys zum Testen verfügbar');
            console.log('📝 Verwende zuerst eine der Scraping-Optionen (1-3)');
            return;
        }

        console.log(`📊 Teste ${allProxies.length} Proxys...`);
        const workingProxies = await this.testProxyList(allProxies);

        // Speichere funktionierende Proxys
        this.proxyManager.workingProxies = workingProxies;
        this.proxyManager.saveProxiesToFile();

        console.log(`✅ ${workingProxies.length} funktionierende Proxys gefunden und in proxys.txt gespeichert`);
    }

    showStats() {
        const stats = this.proxyManager.getStats();
        console.log('\n📊 Proxy-Statistiken:');
        console.log(`Anzahl funktionierender Proxys: ${stats.totalProxies}`);
        console.log(`Verfügbare Länder: ${stats.countries.join(', ')}`);
        console.log(`Protokolle: ${stats.protocols.join(', ')}`);
    }

    // Speichere alle Proxys ungefiltert
    saveAllProxies(proxies) {
        try {
            const proxyStrings = proxies.map(proxy => `${proxy.ip}:${proxy.port}`);
            const fs = require('fs');
            fs.writeFileSync('./proxys_all.txt', proxyStrings.join('\n'));

            // Speichere auch detaillierte Informationen
            fs.writeFileSync('./proxys_all_detailed.json', JSON.stringify(proxies, null, 2));

            console.log(`💾 ${proxyStrings.length} Proxys in proxys_all.txt gespeichert`);
        } catch (error) {
            console.log(`❌ Fehler beim Speichern: ${error.message}`);
        }
    }

    // Lade alle Proxys aus proxys_all.txt
    loadAllProxies() {
        try {
            const fs = require('fs');
            if (fs.existsSync('proxys_all.txt')) {
                const content = fs.readFileSync('proxys_all.txt', 'utf8');
                const proxyLines = content.split('\n').filter(line => line.trim());

                return proxyLines.map(line => {
                    const [ip, port] = line.trim().split(':');
                    return {
                        ip: ip,
                        port: parseInt(port),
                        protocol: 'http',
                        anonymity: 'unknown',
                        speed: 5000,
                        geolocation: { country: 'DE' }
                    };
                });
            }
            return [];
        } catch (error) {
            console.log(`❌ Fehler beim Laden: ${error.message}`);
            return [];
        }
    }

    // Teste eine Liste von Proxys
    async testProxyList(proxies) {
        const workingProxies = [];
        const batchSize = 5; // Teste 5 Proxys parallel

        console.log('🔍 Teste Proxys in Batches für bessere Performance...');

        for (let i = 0; i < proxies.length; i += batchSize) {
            const batch = proxies.slice(i, i + batchSize);
            console.log(`🔄 Teste Batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(proxies.length/batchSize)} (${batch.length} Proxys)...`);

            // Teste Batch parallel
            const batchResults = await Promise.allSettled(
                batch.map(proxy => this.testSingleProxy(proxy))
            );

            // Sammle funktionierende Proxys
            for (let j = 0; j < batch.length; j++) {
                if (batchResults[j].status === 'fulfilled' && batchResults[j].value === true) {
                    workingProxies.push(batch[j]);
                    console.log(`✅ Proxy ${batch[j].ip}:${batch[j].port} funktioniert`);
                }
            }

            // Kurze Pause zwischen Batches
            await this.sleep(1000);
        }

        return workingProxies;
    }

    // Teste einen einzelnen Proxy
    async testSingleProxy(proxy) {
        const axios = require('axios');
        try {
            const response = await axios.get('http://httpbin.org/ip', {
                proxy: {
                    host: proxy.ip,
                    port: proxy.port
                },
                timeout: 10000
            });

            return response.status === 200;
        } catch (error) {
            return false;
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Starte das Tool
async function main() {
    console.log('🚀 EventimBot Proxy-Scraper gestartet');
    console.log('=====================================');
    console.log('Dieses Tool scrapt Proxys und speichert sie in proxys.txt');
    console.log('Der Bot kann dann diese Proxys verwenden.\n');

    const scraper = new ProxyScraper();
    await scraper.showMenu();
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = ProxyScraper;
