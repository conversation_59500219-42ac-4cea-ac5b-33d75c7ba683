// Eventim-Light.com Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxyManager, errorMessages } = require('../eventimbot_main');

async function addTicketsEventimLight(page, quantity) {
    await sleep(2);
    let addToCartFailed = false;

    // Warte auf Quantity-Selector Button
    const incrementButton = await page.waitForSelector('[data-cy="Quantity-Selector_incrementTicketButton"]');

    // Erhöhe Ticket-Anzahl
    for (let i = 0; i < quantity; i++) {
        await incrementButton.click();
        await sleep(1/10);
    }

    // Prüfe aktuelle Quantity
    const currentQuantity = await page.$('.quantity').then(async element =>
        element.evaluate(async el => await el.textContent)
    );

    if (currentQuantity < quantity) {
        console.log(`Can't add the given quantity of tickets. Only ${currentQuantity} tickets will be added`);
    }

    // Scrolle nach unten
    await page.evaluate(() => {
        window.scrollBy(0, 200);
    });
    await sleep(1);

    // Klicke "Add to Cart" Button
    await clickElement(page, '[data-cy="Ticket-Selector_addToCartButton"]');

    // Warte auf Bestätigung
    await page.waitForSelector('.expire-at', { timeout: 40 * 1000 })
        .catch(() => { addToCartFailed = true; });

    if (addToCartFailed) {
        console.log('Add to cart failed');
    } else {
        console.log('Done.');
    }

    await sleep(2);
}

async function processEventimLightEvent(eventIndex, quantity, events) {
    try {
        if (eventIndex !== 0) {
            console.log('\nNext Events =>');
        }

        const eventUrl = events[eventIndex];
        const randomProxy = proxyManager.getRandomProxy();

        if (!randomProxy) {
            console.log('⚠️ Keine Proxys verfügbar, erzwinge Aktualisierung...');
            await proxyManager.forceUpdate();
            const newProxy = proxyManager.getRandomProxy();
            if (!newProxy) {
                throw new Error('Keine funktionierenden Proxys verfügbar');
            }
        }

        const browser = await launchBrowser(randomProxy);
        const page = await browser.newPage();

        // Keine Authentifizierung mehr nötig für Free Proxys

        await page.goto(eventUrl);

        // Prüfe auf Fehler
        const pageContent = await page.content();
        let hasError = false;

        for (const errorMessage of errorMessages) {
            if (pageContent.includes(errorMessage)) {
                hasError = true;
                break;
            }
        }

        if (hasError) {
            console.log('Access Denied, Changing IP Address..');
            // Entferne defekten Proxy aus der Liste
            proxyManager.removeProxy(randomProxy);
            await browser.close().catch(() => null);
            await sleep(10);
            return await processEventimLightEvent(eventIndex, quantity, events);
        }

        console.log(`\nEvent Link: ${eventUrl}`);

        await addTicketsEventimLight(page, quantity);

        try {
            await browser.close();
        } catch (error) {
            // Browser bereits geschlossen
        }

        await sleep(5);

    } catch (error) {
        try {
            await browser.close();
        } catch (closeError) {
            // Browser bereits geschlossen
        }
        console.log(error.message);
    }
}

async function run() {
    const events = fs.readFileSync('./events/eventim-light.com.txt', 'utf-8').split('\n');
    console.log('\nEvents List:', events);

    // Benutzer-Eingaben
    let ticketQuantity;
    while (true) {
        ticketQuantity = parseInt(readlineSync.question('\nPlease enter the quantity number of tickets you would like the bot to add for each event: '));
        if (!isNaN(ticketQuantity)) break;
        console.log('The quantity should be a number. Please try again.');
    }

    let delayAfter10Events;
    while (true) {
        delayAfter10Events = parseInt(readlineSync.question('\nPlease enter the delay that the bot should wait after 10 events. Delay in seconds: '));
        if (!isNaN(delayAfter10Events)) break;
        console.log('The delay should be a number. Please try again.');
    }

    console.log('\nBot Started');

    let eventCounter = 0;

    while (true) {
        for (let i = 0; i < events.length; i++) {
            await processEventimLightEvent(i, ticketQuantity, events);
            eventCounter++;

            if (eventCounter === 10) {
                console.log(`\n10 Events reached. Waiting for ${delayAfter10Events} seconds.\n`);
                await sleep(delayAfter10Events);
                eventCounter = 0;
            }
        }
    }
}

module.exports = { run };
