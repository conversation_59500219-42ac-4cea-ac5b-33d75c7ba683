// MuenchenTicket.net Handler
const fs = require('fs');
const readlineSync = require('readline-sync');
const { sleep, clickElement, launchBrowser, proxyManager, errorMessages } = require('../eventimbot_main');

async function addTicketsMuenchenTicketNET(page, quantity) {
    console.log('\nAdd Tickets Function');
    await sleep(2);

    let imageNotFound = false;
    let ticketsAdded = 0;

    // Prüfe ob Sitzplan-Image vorhanden ist
    await page.waitForSelector('image').catch(() => { imageNotFound = true; });

    if (!imageNotFound) {
        const svgGroups = await page.$$('svg g > g');

        // Iteriere durch SVG-Gruppen (Sitzplätze)
        for (let i = 8; i < svgGroups.length - 5; i++) {
            await page.waitForSelector('image').catch(() => { imageNotFound = true; });

            const currentGroups = await page.$$('svg g > g');
            const currentGroup = currentGroups[i];

            // Klicke auf Sitzplatz
            await currentGroup.evaluate(element => {
                element.dispatchEvent(new MouseEvent('click', { bubbles: false }));
            });
            await sleep(2);

            // Prüfe Tooltips
            const tooltips = await page.$$('[role="tooltip"]');

            if (tooltips.length === 4 &&
                !await tooltips[tooltips.length - 1].evaluate(el =>
                    el.textContent.includes('Nicht ausreichend Plätze verfügbar')
                )) {

                const lastTooltip = tooltips[tooltips.length - 1];
                const button = await lastTooltip.$('button');

                await button.click();
                await sleep(1);

                // Warte auf Sitzplatz-Kreise
                await page.waitForSelector('svg circle').catch(() => { imageNotFound = true; });

                const circles = await page.$$('svg circle');
                let foundAvailableSeat = false;

                // Suche verfügbare Sitzplätze (rote Kreise)
                for (let j = 0; j < circles.length; j++) {
                    const circle = circles[j];

                    const isAvailable = await circle.evaluate(el =>
                        el.style.fill === 'rgb(255, 2, 2)'
                    );

                    if (isAvailable) {
                        foundAvailableSeat = true;

                        await circle.evaluate(el => {
                            el.dispatchEvent(new MouseEvent('click', { bubbles: false }));
                        });

                        ticketsAdded++;

                        if (ticketsAdded === quantity) {
                            break;
                        }
                    }

                    await sleep(1);
                }

                if (foundAvailableSeat) {
                    console.log('Adding to cart');

                    const submitButton = await page.$('[type="submit"]');
                    await submitButton.evaluate(el => el.click());
                    await sleep(1);

                    await page.waitForSelector('#basket_fieldset');
                    console.log('Done');
                }

                if (ticketsAdded === quantity) {
                    break;
                } else {
                    // Navigiere zur nächsten Seite
                    const navLinks = await page.$$('.facelift-flex-nav li a');
                    const nextPageLink = navLinks[2];
                    await nextPageLink.click();
                }
            } else {
                // Klicke auf Body wenn kein Tooltip verfügbar
                const body = await page.$('body');
                await body.click();
                await sleep(1/3);
            }

            await sleep(1);
        }

        if (ticketsAdded === quantity) {
            console.log('Tickets added');
        } else {
            console.log('Tickets can\'t be added');
        }

        await sleep(2);
    } else {
        console.log('Tickets not found');
    }
}

async function processMuenchenTicketNETEvent(eventIndex, quantity, events) {
    try {
        if (eventIndex !== 0) {
            console.log('\nNext Events =>');
        }

        const eventUrl = events[eventIndex];
        const randomProxy = proxyManager.getRandomProxy();

        if (!randomProxy) {
            console.log('⚠️ Keine Proxys verfügbar, erzwinge Aktualisierung...');
            await proxyManager.forceUpdate();
            const newProxy = proxyManager.getRandomProxy();
            if (!newProxy) {
                throw new Error('Keine funktionierenden Proxys verfügbar');
            }
        }

        const browser = await launchBrowser(randomProxy);
        const page = await browser.newPage();

        // Keine Authentifizierung mehr nötig für Free Proxys

        await page.goto(eventUrl);

        // Prüfe auf Fehler
        const pageContent = await page.content();
        let hasError = false;

        for (const errorMessage of errorMessages) {
            if (pageContent.includes(errorMessage)) {
                hasError = true;
                break;
            }
        }

        if (hasError) {
            console.log('Access Denied, Changing IP Address..');
            // Entferne defekten Proxy aus der Liste
            proxyManager.removeProxy(randomProxy);
            await browser.close().catch(() => null);
            await sleep(10);
            return await processMuenchenTicketNETEvent(eventIndex, quantity, events);
        }

        console.log(`\nEvent Link: ${eventUrl}`);

        await addTicketsMuenchenTicketNET(page, quantity);

        try {
            await browser.close();
        } catch (error) {
            // Browser bereits geschlossen
        }

        await sleep(5);

    } catch (error) {
        try {
            await browser.close();
        } catch (closeError) {
            // Browser bereits geschlossen
        }
        console.log(error.message);
    }
}

async function run() {
    const events = fs.readFileSync('./events/muenchenticket.net_shop.txt', 'utf-8').split('\n');
    console.log('\nEvents List:', events);

    // Benutzer-Eingaben
    let ticketQuantity;
    while (true) {
        ticketQuantity = parseInt(readlineSync.question('\nPlease enter the quantity number of tickets you would like the bot to add for each event: '));
        if (!isNaN(ticketQuantity)) break;
        console.log('The quantity should be a number. Please try again.');
    }

    let delayAfter10Events;
    while (true) {
        delayAfter10Events = parseInt(readlineSync.question('\nPlease enter the delay that the bot should wait after 10 events. Delay in seconds: '));
        if (!isNaN(delayAfter10Events)) break;
        console.log('The delay should be a number. Please try again.');
    }

    console.log('\nBot Started');

    let eventCounter = 0;

    while (true) {
        for (let i = 0; i < events.length; i++) {
            await processMuenchenTicketNETEvent(i, ticketQuantity, events);
            eventCounter++;

            if (eventCounter === 10) {
                console.log(`\n10 Events reached. Waiting for ${delayAfter10Events} seconds.\n`);
                await sleep(delayAfter10Events);
                eventCounter = 0;
            }
        }
    }
}

module.exports = { run };
