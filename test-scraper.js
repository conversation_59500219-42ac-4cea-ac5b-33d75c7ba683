#!/usr/bin/env node

const FreeProxyScraper = require('./freeproxy-scraper');

async function testScraper() {
    console.log('🧪 Teste FreeProxy.World Scraper...\n');

    try {
        console.log('🚀 Starte Scraper-Test...');
        
        const startTime = Date.now();
        const proxies = await FreeProxyScraper.scrapeProxies();
        const endTime = Date.now();
        
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        console.log('\n📊 Scraping-Ergebnisse:');
        console.log(`⏱️ Dauer: ${duration} Sekunden`);
        console.log(`📦 Gefundene Proxys: ${proxies.length}`);
        
        if (proxies.length > 0) {
            console.log('\n🔍 Erste 5 Proxys:');
            proxies.slice(0, 5).forEach((proxy, index) => {
                console.log(`${index + 1}. ${proxy.ip}:${proxy.port} (${proxy.protocol}, ${proxy.speed}ms, ${proxy.anonymity})`);
            });
            
            // Statistiken
            const countries = [...new Set(proxies.map(p => p.geolocation?.country))];
            const protocols = [...new Set(proxies.map(p => p.protocol))];
            const avgSpeed = Math.round(proxies.reduce((sum, p) => sum + p.speed, 0) / proxies.length);
            
            console.log('\n📈 Statistiken:');
            console.log(`🌍 Länder: ${countries.join(', ')}`);
            console.log(`🔗 Protokolle: ${protocols.join(', ')}`);
            console.log(`⚡ Durchschnittliche Geschwindigkeit: ${avgSpeed}ms`);
            
            // Geschwindigkeitsverteilung
            const fast = proxies.filter(p => p.speed < 1000).length;
            const medium = proxies.filter(p => p.speed >= 1000 && p.speed < 3000).length;
            const slow = proxies.filter(p => p.speed >= 3000).length;
            
            console.log('\n🚀 Geschwindigkeitsverteilung:');
            console.log(`⚡ Schnell (<1s): ${fast}`);
            console.log(`🚶 Mittel (1-3s): ${medium}`);
            console.log(`🐌 Langsam (>3s): ${slow}`);
            
        } else {
            console.log('❌ Keine Proxys gefunden');
        }
        
        console.log('\n✅ Scraper-Test abgeschlossen!');
        
    } catch (error) {
        console.error('❌ Fehler beim Testen des Scrapers:', error.message);
        console.log('\n🔧 Mögliche Ursachen:');
        console.log('• Cloudflare-Schutz aktiv');
        console.log('• Netzwerkprobleme');
        console.log('• Website-Struktur geändert');
        console.log('• Browser-Probleme');
    }
}

// Starte Test
testScraper();
