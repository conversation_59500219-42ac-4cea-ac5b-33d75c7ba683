const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
puppeteer.use(StealthPlugin());

class FreeProxyScraper {
    constructor() {
        this.baseUrl = 'https://www.freeproxy.world/';
        this.countries = ['DE', 'AT', 'CH']; // Deutschland, Österreich, Schweiz
        this.maxPages = 5; // Maximal 5 Seiten pro Land scrapen
        this.browser = null;
        this.page = null;
    }

    async init() {
        console.log('🚀 Initialisiere FreeProxy Scraper...');
        this.browser = await puppeteer.launch({
            headless: true, // Headless für bessere Performance
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu'
            ]
        });
        this.page = await this.browser.newPage();

        // User-Agent setzen um wie ein echter Browser auszusehen
        await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

        // Viewport setzen
        await this.page.setViewport({ width: 1366, height: 768 });

        console.log('✅ Scraper initialisiert');
    }

    async scrapeCountryProxies(country) {
        console.log(`🌍 Scrape Proxys aus ${country}...`);
        const allProxies = [];

        try {
            // Gehe zur Länder-spezifischen Seite
            const url = `${this.baseUrl}?country=${country}`;
            console.log(`📡 Lade ${url}...`);

            await this.page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: 30000
            });

            // Warte kurz und prüfe auf Cloudflare Challenge
            await this.sleep(3000);

            if (await this.handleCloudflareChallenge()) {
                console.log('🔒 Cloudflare Challenge erkannt und behandelt');
                await this.sleep(5000);
            }

            // Scrape mehrere Seiten
            for (let pageNum = 1; pageNum <= this.maxPages; pageNum++) {
                console.log(`📄 Scrape Seite ${pageNum} für ${country}...`);

                try {
                    // Wenn nicht die erste Seite, navigiere zur nächsten Seite
                    if (pageNum > 1) {
                        await this.navigateToPage(pageNum);
                        await this.sleep(2000);
                    }

                    const pageProxies = await this.scrapeCurrentPage();
                    allProxies.push(...pageProxies);

                    console.log(`✓ ${pageProxies.length} Proxys von Seite ${pageNum} gefunden`);

                    // Prüfe ob es weitere Seiten gibt
                    const hasNextPage = await this.hasNextPage();
                    if (!hasNextPage) {
                        console.log(`📄 Keine weiteren Seiten für ${country} verfügbar`);
                        break;
                    }

                } catch (pageError) {
                    console.log(`⚠️ Fehler beim Scrapen von Seite ${pageNum}: ${pageError.message}`);
                    break;
                }
            }

        } catch (error) {
            console.log(`❌ Fehler beim Scrapen von ${country}: ${error.message}`);
        }

        console.log(`✅ ${allProxies.length} Proxys aus ${country} gesammelt`);
        return allProxies;
    }

    async handleCloudflareChallenge() {
        try {
            // Prüfe auf Cloudflare Challenge Indikatoren
            const challengeSelectors = [
                '#challenge-form',
                '.cf-challenge',
                '[data-ray]',
                'title:contains("Just a moment")',
                'h1:contains("Checking your browser")'
            ];

            for (const selector of challengeSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        console.log('🔒 Cloudflare Challenge erkannt, warte...');

                        // Warte bis zu 30 Sekunden auf die Weiterleitung
                        await this.page.waitForNavigation({
                            waitUntil: 'networkidle2',
                            timeout: 30000
                        });

                        return true;
                    }
                } catch (e) {
                    // Selector nicht gefunden, weiter
                }
            }

            return false;
        } catch (error) {
            console.log('⚠️ Fehler bei Cloudflare Challenge Behandlung:', error.message);
            return false;
        }
    }

    async scrapeCurrentPage() {
        try {
            // Warte auf die Proxy-Tabelle mit verschiedenen Selektoren
            const tableSelectors = [
                '.layui-table tbody tr',
                'table tbody tr',
                '.proxy-table table tr',
                'tr'
            ];

            let tableFound = false;
            for (const selector of tableSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    tableFound = true;
                    console.log(`✓ Tabelle gefunden mit Selector: ${selector}`);
                    break;
                } catch (e) {
                    continue;
                }
            }

            if (!tableFound) {
                console.log('⚠️ Keine Proxy-Tabelle gefunden');

                // Debug: Zeige Seiteninhalt
                const pageTitle = await this.page.title();
                const url = this.page.url();
                console.log(`Debug: Seite "${pageTitle}" auf ${url}`);

                // Prüfe auf Cloudflare oder andere Blocker
                const bodyText = await this.page.evaluate(() => document.body.textContent);
                if (bodyText.includes('Cloudflare') || bodyText.includes('Just a moment')) {
                    console.log('🔒 Cloudflare-Schutz erkannt');
                } else if (bodyText.includes('Access Denied') || bodyText.includes('Forbidden')) {
                    console.log('🚫 Zugriff verweigert');
                } else {
                    console.log('🔍 Unbekanntes Problem, Seite geladen aber keine Tabelle gefunden');
                }

                return [];
            }

            const proxies = await this.page.evaluate(() => {
                // Versuche verschiedene Selektoren für die Tabelle
                const tableSelectors = [
                    '.layui-table tbody tr',
                    'table tbody tr',
                    '.proxy-table table tbody tr',
                    'tbody tr'
                ];

                let rows = [];
                for (const selector of tableSelectors) {
                    rows = document.querySelectorAll(selector);
                    if (rows.length > 0) {
                        console.log(`Verwende Selector: ${selector}, gefunden: ${rows.length} Zeilen`);
                        break;
                    }
                }

                const proxies = [];

                rows.forEach((row, index) => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 4) {
                        // Verschiedene Möglichkeiten für IP-Extraktion
                        let ip = null;
                        let port = null;

                        // IP aus verschiedenen Quellen extrahieren
                        const ipSources = [
                            cells[0]?.querySelector('.show-ip-div'),
                            cells[0]?.querySelector('td'),
                            cells[0]
                        ];

                        for (const source of ipSources) {
                            if (source && source.textContent) {
                                const text = source.textContent.trim();
                                // IP-Regex
                                const ipMatch = text.match(/\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b/);
                                if (ipMatch) {
                                    ip = ipMatch[0];
                                    break;
                                }
                            }
                        }

                        // Port aus verschiedenen Quellen extrahieren
                        const portSources = [
                            cells[1]?.querySelector('a'),
                            cells[1]
                        ];

                        for (const source of portSources) {
                            if (source && source.textContent) {
                                const text = source.textContent.trim();
                                const portMatch = text.match(/\b[0-9]{1,5}\b/);
                                if (portMatch && parseInt(portMatch[0]) > 0 && parseInt(portMatch[0]) <= 65535) {
                                    port = parseInt(portMatch[0]);
                                    break;
                                }
                            }
                        }

                        // Geschwindigkeit extrahieren
                        let speed = 5000;
                        const speedSources = [
                            cells[4]?.querySelector('.bar p a'),
                            cells[4]?.querySelector('a'),
                            cells[4]
                        ];

                        for (const source of speedSources) {
                            if (source && source.textContent) {
                                const speedMatch = source.textContent.match(/\b[0-9]+\b/);
                                if (speedMatch) {
                                    speed = parseInt(speedMatch[0]);
                                    break;
                                }
                            }
                        }

                        // Typ extrahieren
                        let type = 'http';
                        if (cells[5]) {
                            const typeText = cells[5].textContent.toLowerCase();
                            if (typeText.includes('socks5')) type = 'socks5';
                            else if (typeText.includes('socks4')) type = 'socks4';
                            else if (typeText.includes('https')) type = 'https';
                        }

                        // Anonymität extrahieren
                        let anonymity = 'anonymous';
                        if (cells[6]) {
                            const anonText = cells[6].textContent.toLowerCase();
                            if (anonText.includes('high') || anonText.includes('elite')) {
                                anonymity = 'elite';
                            }
                        }

                        // Proxy hinzufügen wenn IP und Port gefunden
                        if (ip && port && speed < 8000) {
                            proxies.push({
                                ip: ip,
                                port: port,
                                protocol: type,
                                speed: speed,
                                anonymity: anonymity,
                                geolocation: { country: 'DE' }
                            });
                        }
                    }
                });

                return proxies;
            });

            return proxies;
        } catch (error) {
            console.log('⚠️ Fehler beim Scrapen der aktuellen Seite:', error.message);
            return [];
        }
    }

    async navigateToPage(pageNum) {
        try {
            // Versuche verschiedene Methoden zur Seitennavigation

            // Methode 1: Direkte URL mit page Parameter
            const currentUrl = this.page.url();
            const url = new URL(currentUrl);
            url.searchParams.set('page', pageNum - 1); // 0-basiert

            await this.page.goto(url.toString(), {
                waitUntil: 'networkidle2',
                timeout: 15000
            });

        } catch (error) {
            console.log(`⚠️ Fehler bei Navigation zu Seite ${pageNum}: ${error.message}`);
            throw error;
        }
    }

    async hasNextPage() {
        try {
            // Prüfe ob es weitere Seiten gibt
            const pagination = await this.page.$('.proxy_table_pages');
            if (!pagination) return false;

            const dataCounts = await this.page.evaluate(el => el.getAttribute('data-counts'), pagination);
            const currentProxies = await this.page.$$('.layui-table tbody tr');

            // Wenn weniger als 50 Proxys auf der Seite, wahrscheinlich letzte Seite
            return currentProxies.length >= 40;

        } catch (error) {
            return false;
        }
    }

    async scrapeAllCountries() {
        console.log('🌍 Starte Scraping aller Länder...');
        const allProxies = [];

        for (const country of this.countries) {
            try {
                const countryProxies = await this.scrapeCountryProxies(country);

                // Setze das korrekte Land
                countryProxies.forEach(proxy => {
                    proxy.geolocation.country = country;
                });

                allProxies.push(...countryProxies);

                // Pause zwischen Ländern
                await this.sleep(3000);

            } catch (error) {
                console.log(`❌ Fehler beim Scrapen von ${country}: ${error.message}`);
            }
        }

        console.log(`🎉 Scraping abgeschlossen: ${allProxies.length} Proxys gesammelt`);
        return allProxies;
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            console.log('🔒 Browser geschlossen');
        }
    }

    // Statische Methode für einfache Verwendung
    static async scrapeProxies() {
        const scraper = new FreeProxyScraper();
        try {
            await scraper.init();
            const proxies = await scraper.scrapeAllCountries();
            return proxies;
        } finally {
            await scraper.close();
        }
    }
}

module.exports = FreeProxyScraper;
