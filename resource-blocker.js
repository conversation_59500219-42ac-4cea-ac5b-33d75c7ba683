// EventimBot - Resource Blocker für Datenverbrauch-Optimierung
const DataOptimizationConfig = require('./data-optimization-config');

class ResourceBlocker {
    constructor(optimizationLevel = 'moderate') {
        this.config = DataOptimizationConfig;
        this.level = optimizationLevel;
        this.settings = this.config.levels[optimizationLevel];
        this.blockedCount = {
            images: 0,
            fonts: 0,
            analytics: 0,
            ads: 0,
            socialMedia: 0,
            videos: 0,
            css: 0,
            other: 0
        };
        this.allowedCount = 0;
        this.totalRequests = 0;
    }

    // Initialisiere Resource-Blocking für eine Page
    async setupResourceBlocking(page) {
        console.log(`🛡️ Aktiviere Resource-Blocking (Level: ${this.level})...`);
        
        await page.setRequestInterception(true);
        
        page.on('request', (request) => {
            this.totalRequests++;
            const url = request.url();
            const resourceType = request.resourceType();
            
            // Prüfe ob Ressource blockiert werden soll
            const shouldBlock = this.shouldBlockResource(url, resourceType);
            
            if (shouldBlock.block) {
                this.blockedCount[shouldBlock.category]++;
                console.log(`🚫 Blockiert (${shouldBlock.category}): ${this.truncateUrl(url)}`);
                request.abort();
            } else {
                this.allowedCount++;
                // Optional: Logge erlaubte kritische Ressourcen
                if (shouldBlock.reason) {
                    console.log(`✅ Erlaubt (${shouldBlock.reason}): ${this.truncateUrl(url)}`);
                }
                request.continue();
            }
        });

        // Setze zusätzliche Page-Optimierungen
        await this.setupPageOptimizations(page);
    }

    // Prüfe ob eine Ressource blockiert werden soll
    shouldBlockResource(url, resourceType) {
        const urlLower = url.toLowerCase();

        // 1. Kritische Eventim-Ressourcen NIEMALS blockieren
        if (this.matchesPatterns(urlLower, this.config.resourcePatterns.eventimCritical)) {
            return { block: false, reason: 'eventim-critical' };
        }

        // 2. Document und XHR/Fetch NIEMALS blockieren (für Bot-Funktionalität)
        if (resourceType === 'document' || resourceType === 'xhr' || resourceType === 'fetch') {
            return { block: false, reason: 'critical-request' };
        }

        // 3. Bilder blockieren (je nach Einstellung)
        if (resourceType === 'image' || this.matchesPatterns(urlLower, this.config.resourcePatterns.images)) {
            if (this.settings.blockImages) {
                // Ausnahme: Kritische Eventim-Bilder bei moderater Optimierung
                if (this.settings.allowEventimImages && this.isEventimImage(urlLower)) {
                    return { block: false, reason: 'eventim-image' };
                }
                return { block: true, category: 'images' };
            }
        }

        // 4. Fonts blockieren
        if (resourceType === 'font' || this.matchesPatterns(urlLower, this.config.resourcePatterns.fonts)) {
            if (this.settings.blockFonts) {
                return { block: true, category: 'fonts' };
            }
        }

        // 5. Analytics & Tracking blockieren
        if (this.matchesPatterns(urlLower, this.config.resourcePatterns.analytics)) {
            if (this.settings.blockAnalytics) {
                return { block: true, category: 'analytics' };
            }
        }

        // 6. Werbung blockieren
        if (this.matchesPatterns(urlLower, this.config.resourcePatterns.ads)) {
            if (this.settings.blockAds) {
                return { block: true, category: 'ads' };
            }
        }

        // 7. Social Media blockieren
        if (this.matchesPatterns(urlLower, this.config.resourcePatterns.socialMedia)) {
            if (this.settings.blockSocialMedia) {
                return { block: true, category: 'socialMedia' };
            }
        }

        // 8. Videos blockieren
        if (resourceType === 'media' || this.matchesPatterns(urlLower, this.config.resourcePatterns.videos)) {
            if (this.settings.blockVideos) {
                return { block: true, category: 'videos' };
            }
        }

        // 9. CSS prüfen
        if (resourceType === 'stylesheet') {
            if (this.settings.blockNonEssentialCSS) {
                // Bei aggressiver Optimierung nur kritische CSS erlauben
                if (!this.matchesPatterns(urlLower, this.config.resourcePatterns.criticalCSS)) {
                    return { block: true, category: 'css' };
                }
            } else if (!this.settings.allowCriticalCSS) {
                return { block: true, category: 'css' };
            }
        }

        // 10. JavaScript prüfen
        if (resourceType === 'script') {
            if (!this.settings.allowCriticalJS) {
                // Nur bei sehr aggressiver Optimierung
                if (!this.matchesPatterns(urlLower, this.config.resourcePatterns.criticalJS)) {
                    return { block: true, category: 'other' };
                }
            }
        }

        // Standardmäßig erlauben
        return { block: false };
    }

    // Prüfe ob URL zu Eventim-Bildern gehört
    isEventimImage(url) {
        return this.config.eventimOptimizations.allowedImageDomains.some(domain => 
            url.includes(domain.toLowerCase())
        );
    }

    // Prüfe ob URL zu Patterns passt
    matchesPatterns(url, patterns) {
        return patterns.some(pattern => {
            if (pattern instanceof RegExp) {
                return pattern.test(url);
            }
            return url.includes(pattern.toLowerCase());
        });
    }

    // Zusätzliche Page-Optimierungen
    async setupPageOptimizations(page) {
        // Deaktiviere Cache
        await page.setCacheEnabled(false);

        // Setze User-Agent (wichtig für Eventim)
        await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

        // Setze Viewport (reduziert Bildgrößen)
        await page.setViewport({ 
            width: 1280, 
            height: 720,
            deviceScaleFactor: 1 // Verhindert hochauflösende Bilder
        });

        // Deaktiviere JavaScript-Animationen (spart CPU und Daten)
        await page.evaluateOnNewDocument(() => {
            // Deaktiviere CSS-Animationen
            const style = document.createElement('style');
            style.textContent = `
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                    scroll-behavior: auto !important;
                }
            `;
            document.head.appendChild(style);
        });
    }

    // URL für Logging kürzen
    truncateUrl(url, maxLength = 80) {
        if (url.length <= maxLength) return url;
        return url.substring(0, maxLength - 3) + '...';
    }

    // Statistiken anzeigen
    showStatistics() {
        const totalBlocked = Object.values(this.blockedCount).reduce((sum, count) => sum + count, 0);
        const blockingPercentage = this.totalRequests > 0 ? ((totalBlocked / this.totalRequests) * 100).toFixed(1) : 0;
        
        console.log('\n📊 Resource-Blocking Statistiken:');
        console.log('=====================================');
        console.log(`Optimierungslevel: ${this.level}`);
        console.log(`Gesamt Requests: ${this.totalRequests}`);
        console.log(`Blockiert: ${totalBlocked} (${blockingPercentage}%)`);
        console.log(`Erlaubt: ${this.allowedCount}`);
        console.log('\nBlockiert nach Kategorie:');
        Object.entries(this.blockedCount).forEach(([category, count]) => {
            if (count > 0) {
                console.log(`  ${category}: ${count}`);
            }
        });
        console.log('=====================================\n');
    }

    // Reset Statistiken
    resetStatistics() {
        this.blockedCount = {
            images: 0,
            fonts: 0,
            analytics: 0,
            ads: 0,
            socialMedia: 0,
            videos: 0,
            css: 0,
            other: 0
        };
        this.allowedCount = 0;
        this.totalRequests = 0;
    }

    // Optimierungslevel ändern
    setOptimizationLevel(level) {
        if (this.config.levels[level]) {
            this.level = level;
            this.settings = this.config.levels[level];
            console.log(`🔧 Optimierungslevel geändert zu: ${level}`);
        } else {
            console.log(`❌ Unbekanntes Optimierungslevel: ${level}`);
        }
    }
}

module.exports = ResourceBlocker;
