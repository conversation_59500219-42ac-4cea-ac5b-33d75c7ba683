#!/usr/bin/env node

const ProxyManager = require('./proxy-manager');
const readlineSync = require('readline-sync');

class ProxyTool {
    constructor() {
        this.proxyManager = new ProxyManager();
    }

    async showMenu() {
        console.log('\n🔧 EventimBot Proxy-Verwaltung');
        console.log('================================');
        console.log('1. Proxy-Statistiken anzeigen');
        console.log('2. Proxys manuell aktualisieren');
        console.log('3. Proxy-Liste anzeigen');
        console.log('4. Proxy testen');
        console.log('5. Defekten Proxy entfernen');
        console.log('6. FreeProxy.World scrapen');
        console.log('7. Kontinuierliche Überwachung starten');
        console.log('0. Beenden');
        console.log('================================');

        const choice = readlineSync.question('Wählen Sie eine Option: ');
        await this.handleChoice(choice);
    }

    async handleChoice(choice) {
        switch (choice) {
            case '1':
                this.showStats();
                break;
            case '2':
                await this.updateProxies();
                break;
            case '3':
                this.showProxyList();
                break;
            case '4':
                await this.testProxy();
                break;
            case '5':
                this.removeProxy();
                break;
            case '6':
                await this.scrapeFreeProxyWorld();
                break;
            case '7':
                await this.startMonitoring();
                break;
            case '0':
                console.log('👋 Auf Wiedersehen!');
                process.exit(0);
                break;
            default:
                console.log('❌ Ungültige Auswahl');
        }

        // Zurück zum Menü
        await this.sleep(2000);
        await this.showMenu();
    }

    showStats() {
        const stats = this.proxyManager.getStats();
        console.log('\n📊 Proxy-Statistiken:');
        console.log(`Anzahl funktionierender Proxys: ${stats.totalProxies}`);
        console.log(`Letzte Aktualisierung: ${stats.lastUpdate}`);
        console.log(`Verfügbare Länder: ${stats.countries.join(', ')}`);
        console.log(`Protokolle: ${stats.protocols.join(', ')}`);
    }

    async updateProxies() {
        console.log('\n🔄 Starte manuelle Proxy-Aktualisierung...');
        await this.proxyManager.forceUpdate();
        console.log('✅ Aktualisierung abgeschlossen');
    }

    showProxyList() {
        const proxies = this.proxyManager.getProxyList();
        console.log('\n📋 Aktuelle Proxy-Liste:');
        if (proxies.length === 0) {
            console.log('❌ Keine Proxys verfügbar');
        } else {
            proxies.forEach((proxy, index) => {
                console.log(`${index + 1}. ${proxy}`);
            });
        }
    }

    async testProxy() {
        const proxies = this.proxyManager.getProxyList();
        if (proxies.length === 0) {
            console.log('❌ Keine Proxys zum Testen verfügbar');
            return;
        }

        console.log('\n🧪 Verfügbare Proxys zum Testen:');
        proxies.forEach((proxy, index) => {
            console.log(`${index + 1}. ${proxy}`);
        });

        const choice = readlineSync.question('Proxy-Nummer zum Testen (oder Enter für zufälligen): ');

        let proxyToTest;
        if (choice === '') {
            proxyToTest = this.proxyManager.getRandomProxy();
        } else {
            const index = parseInt(choice) - 1;
            if (index >= 0 && index < proxies.length) {
                proxyToTest = proxies[index];
            } else {
                console.log('❌ Ungültige Auswahl');
                return;
            }
        }

        console.log(`\n🔍 Teste Proxy: ${proxyToTest}`);
        const [ip, port] = proxyToTest.split(':');
        const isWorking = await this.proxyManager.testProxy({ ip, port: parseInt(port) });

        if (isWorking) {
            console.log('✅ Proxy funktioniert');
        } else {
            console.log('❌ Proxy funktioniert nicht');
            const remove = readlineSync.keyInYN('Soll der defekte Proxy entfernt werden?');
            if (remove) {
                this.proxyManager.removeProxy(proxyToTest);
                console.log('🗑️ Proxy entfernt');
            }
        }
    }

    removeProxy() {
        const proxies = this.proxyManager.getProxyList();
        if (proxies.length === 0) {
            console.log('❌ Keine Proxys zum Entfernen verfügbar');
            return;
        }

        console.log('\n🗑️ Verfügbare Proxys zum Entfernen:');
        proxies.forEach((proxy, index) => {
            console.log(`${index + 1}. ${proxy}`);
        });

        const choice = readlineSync.question('Proxy-Nummer zum Entfernen: ');
        const index = parseInt(choice) - 1;

        if (index >= 0 && index < proxies.length) {
            const proxyToRemove = proxies[index];
            const confirm = readlineSync.keyInYN(`Proxy ${proxyToRemove} wirklich entfernen?`);
            if (confirm) {
                this.proxyManager.removeProxy(proxyToRemove);
                console.log('✅ Proxy entfernt');
            }
        } else {
            console.log('❌ Ungültige Auswahl');
        }
    }

    async scrapeFreeProxyWorld() {
        console.log('\n🕷️ Starte FreeProxy.World Scraping...');
        console.log('Dies kann einige Minuten dauern...');

        try {
            const beforeCount = this.proxyManager.getProxyCount();
            const scrapedProxies = await this.proxyManager.scrapeFreeProxyWorld();

            if (scrapedProxies.length > 0) {
                console.log(`✅ ${scrapedProxies.length} neue Proxys gescraped`);

                // Teste die neuen Proxys
                console.log('🔍 Teste neue Proxys...');
                let workingCount = 0;
                for (const proxy of scrapedProxies.slice(0, 10)) { // Teste nur die ersten 10
                    const isWorking = await this.proxyManager.testProxy(proxy);
                    if (isWorking) workingCount++;
                }

                console.log(`✅ ${workingCount} von ${Math.min(10, scrapedProxies.length)} getesteten Proxys funktionieren`);

                // Aktualisiere die Proxy-Liste
                await this.proxyManager.forceUpdate();
                const afterCount = this.proxyManager.getProxyCount();
                console.log(`📊 Proxy-Anzahl: ${beforeCount} → ${afterCount}`);

            } else {
                console.log('⚠️ Keine Proxys gescraped');
            }

        } catch (error) {
            console.log(`❌ Fehler beim Scraping: ${error.message}`);
        }
    }

    async startMonitoring() {
        console.log('\n👁️ Starte kontinuierliche Proxy-Überwachung...');
        console.log('Drücken Sie Ctrl+C zum Beenden');

        while (true) {
            const stats = this.proxyManager.getStats();
            console.log(`\n[${new Date().toLocaleTimeString('de-DE')}] Proxys: ${stats.totalProxies} | Letzte Aktualisierung: ${stats.lastUpdate}`);

            // Warte 30 Sekunden
            await this.sleep(30000);
        }
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Starte das Tool
async function main() {
    console.log('🚀 Starte Proxy-Tool...');
    const tool = new ProxyTool();

    // Warte kurz, damit der ProxyManager initialisiert wird
    await tool.sleep(3000);

    await tool.showMenu();
}

// Nur ausführen, wenn direkt aufgerufen
if (require.main === module) {
    main().catch(console.error);
}

module.exports = ProxyTool;
